.card {
  border: 1px solid black;
  transition: 500ms ease;

  &.currently-playing {
    border-color: var(--bs-primary);
  }
}

.card-lang-indicator {
  font-size: 0.75rem;
  font-weight: 700;
}

.card-content {
  font-size: 1.25rem;
}

.card-divider {
  height: 1px;
  background: black;
}

.delete-icon {
  font-size: 1.5rem;
  &:hover {
    cursor: pointer;
  }
}

.soundwave-icon {
  font-size: 1.5rem;
}

.playing-indicator {
  position: absolute;
  top: 1rem;
  right: 0.5rem;
  font-size: 1.5rem;
  color: var(--bs-primary);
  opacity: 0;
  transition: 500ms ease;

  &.visible {
    opacity: 1;
  }
}
