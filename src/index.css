:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html,
body {
  margin: 0;
  padding: 0;
}

html,
body,
#root {
  height: 100%;
}

* {
  box-sizing: border-box;
}

h1 {
  font-size: 24px;
}

/* Works on Chrome, Edge, and Safari */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #fff8ce;
  border-radius: 10px;
  border: 2px solid #f1f1f1;
}

::-webkit-scrollbar-thumb:hover {
  background: #fff8ce;
}

/* Works on Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #fff8ce #f1f1f1;
}

/* Dev */

.zzz {
  border: 1px solid red;
}