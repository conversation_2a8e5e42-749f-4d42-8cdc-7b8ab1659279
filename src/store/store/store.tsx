// Redux
import { configureStore } from "@reduxjs/toolkit";
// Actions
import { ADD_CARD, EDIT_CARD, DELETE_CARD, SET_CARDS } from "./actionTypes.js";
// Constants
import { Card } from "../../Interfaces.js";
import { getCards } from "../../utilites/localStorage.js";

const initialState = {
  cards: getCards(),
};

export function addNewCard(card: Card) {
  return {
    type: ADD_CARD,
    data: card,
  };
}

export function editCard(card: Card) {
  return {
    type: EDIT_CARD,
    data: card,
  };
}

export function deleteCard(card: Card) {
  return {
    type: DELETE_CARD,
    data: card,
  };
}

// export function setTasks(tasks) {
//   return {
//     type: SET_TASKS,
//     data: tasks,
//   };
// }

const tasksReducer = (
  state = initialState,
  action: { type: string; data: Card }
) => {
  switch (action.type) {
    case ADD_CARD:
      return {
        ...state,
        cards: [...state.cards, action.data],
      };
    case EDIT_CARD:
      return {
        ...state,
        cards: state.cards.map((c: Card) => {
          if (c.id === action.data.id) {
            return {
              ...c,
              ...action.data,
            };
          } else {
            return c;
          }
        }),
      };
    case DELETE_CARD:
      return {
        ...state,
        cards: state.cards.filter((c: Card) => c.id !== action.data.id),
      };
    case SET_CARDS:
      return {
        ...state,
        tasks: action.data,
      };
    default:
      return state;
  }
};

const store = configureStore({
  reducer: tasksReducer,
});
export default store;
