import { LangCardsSettings, LanguageOption } from "../Interfaces";

export const LANGUAGE_OPTIONS: LanguageOption[] = [
  { isoLang: "ab", label: "Abkhazian" },
  { isoLang: "aa", label: "Afar" },
  { isoLang: "af", label: "Afrikaans" },
  { isoLang: "ak", label: "Akan" },
  { isoLang: "sq", label: "Albanian" },
  { isoLang: "am", label: "Amharic" },
  { isoLang: "ar", label: "Arabic" },
  { isoLang: "an", label: "Aragonese" },
  { isoLang: "hy", label: "Armenian" },
  { isoLang: "as", label: "Assamese" },
  { isoLang: "av", label: "Avaric" },
  { isoLang: "ae", label: "Avestan" },
  { isoLang: "ay", label: "Aymara" },
  { isoLang: "az", label: "Azerbaijani" },
  { isoLang: "bm", label: "<PERSON><PERSON><PERSON>" },
  { isoLang: "ba", label: "Bashkir" },
  { isoLang: "eu", label: "Basque" },
  { isoLang: "be", label: "Belarusian" },
  { isoLang: "bn", label: "Bengali" },
  { isoLang: "bi", label: "Bislama" },
  { isoLang: "bs", label: "Bosnian" },
  { isoLang: "br", label: "Breton" },
  { isoLang: "bg", label: "Bulgarian" },
  { isoLang: "my", label: "Burmese" },
  { isoLang: "ca", label: "Catalan" },
  { isoLang: "ch", label: "Chamorro" },
  { isoLang: "ce", label: "Chechen" },
  { isoLang: "ny", label: "Chichewa" },
  { isoLang: "zh", label: "Chinese" },
  { isoLang: "cu", label: "Church Slavic" },
  { isoLang: "cv", label: "Chuvash" },
  { isoLang: "kw", label: "Cornish" },
  { isoLang: "co", label: "Corsican" },
  { isoLang: "cr", label: "Cree" },
  { isoLang: "hr", label: "Croatian" },
  { isoLang: "cs", label: "Czech" },
  { isoLang: "da", label: "Danish" },
  { isoLang: "dv", label: "Divehi" },
  { isoLang: "nl", label: "Dutch" },
  { isoLang: "dz", label: "Dzongkha" },
  { isoLang: "en", label: "English" },
  { isoLang: "eo", label: "Esperanto" },
  { isoLang: "et", label: "Estonian" },
  { isoLang: "ee", label: "Ewe" },
  { isoLang: "fo", label: "Faroese" },
  { isoLang: "fj", label: "Fijian" },
  { isoLang: "fi", label: "Finnish" },
  { isoLang: "fr", label: "French" },
  { isoLang: "fy", label: "Western Frisian" },
  { isoLang: "ff", label: "Fulah" },
  { isoLang: "gd", label: "Gaelic" },
  { isoLang: "gl", label: "Galician" },
  { isoLang: "lg", label: "Ganda" },
  { isoLang: "ka", label: "Georgian" },
  { isoLang: "de", label: "German" },
  { isoLang: "el", label: "Greek" },
  { isoLang: "kl", label: "Kalaallisut" },
  { isoLang: "gn", label: "Guarani" },
  { isoLang: "gu", label: "Gujarati" },
  { isoLang: "ht", label: "Haitian" },
  { isoLang: "ha", label: "Hausa" },
  { isoLang: "he", label: "Hebrew" },
  { isoLang: "hz", label: "Herero" },
  { isoLang: "hi", label: "Hindi" },
  { isoLang: "ho", label: "Hiri Motu" },
  { isoLang: "hu", label: "Hungarian" },
  { isoLang: "is", label: "Icelandic" },
  { isoLang: "io", label: "Ido" },
  { isoLang: "ig", label: "Igbo" },
  { isoLang: "id", label: "Indonesian" },
  { isoLang: "ia", label: "Interlingua" },
  { isoLang: "ie", label: "Interlingue" },
  { isoLang: "iu", label: "Inuktitut" },
  { isoLang: "ik", label: "Inupiaq" },
  { isoLang: "ga", label: "Irish" },
  { isoLang: "it", label: "Italian" },
  { isoLang: "ja", label: "Japanese" },
  { isoLang: "jv", label: "Javanese" },
  { isoLang: "kn", label: "Kannada" },
  { isoLang: "kr", label: "Kanuri" },
  { isoLang: "ks", label: "Kashmiri" },
  { isoLang: "kk", label: "Kazakh" },
  { isoLang: "km", label: "Khmer" },
  { isoLang: "ki", label: "Kikuyu" },
  { isoLang: "rw", label: "Kinyarwanda" },
  { isoLang: "ky", label: "Kirghiz" },
  { isoLang: "kv", label: "Komi" },
  { isoLang: "kg", label: "Kongo" },
  { isoLang: "ko", label: "Korean" },
  { isoLang: "kj", label: "Kuanyama" },
  { isoLang: "ku", label: "Kurdish" },
  { isoLang: "lo", label: "Lao" },
  { isoLang: "la", label: "Latin" },
  { isoLang: "lv", label: "Latvian" },
  { isoLang: "li", label: "Limburgish" },
  { isoLang: "ln", label: "Lingala" },
  { isoLang: "lt", label: "Lithuanian" },
  { isoLang: "lu", label: "Luba-Katanga" },
  { isoLang: "lb", label: "Luxembourgish" },
  { isoLang: "mk", label: "Macedonian" },
  { isoLang: "mg", label: "Malagasy" },
  { isoLang: "ms", label: "Malay" },
  { isoLang: "ml", label: "Malayalam" },
  { isoLang: "mt", label: "Maltese" },
  { isoLang: "gv", label: "Manx" },
  { isoLang: "mi", label: "Maori" },
  { isoLang: "mr", label: "Marathi" },
  { isoLang: "mh", label: "Marshallese" },
  { isoLang: "mn", label: "Mongolian" },
  { isoLang: "na", label: "Nauru" },
  { isoLang: "nv", label: "Navajo" },
  { isoLang: "nd", label: "North Ndebele" },
  { isoLang: "nr", label: "South Ndebele" },
  { isoLang: "ng", label: "Ndonga" },
  { isoLang: "ne", label: "Nepali" },
  { isoLang: "no", label: "Norwegian" },
  { isoLang: "nb", label: "Norwegian Bokmål" },
  { isoLang: "nn", label: "Norwegian Nynorsk" },
  { isoLang: "oc", label: "Occitan" },
  { isoLang: "oj", label: "Ojibwa" },
  { isoLang: "or", label: "Oriya" },
  { isoLang: "om", label: "Oromo" },
  { isoLang: "os", label: "Ossetian" },
  { isoLang: "pi", label: "Pali" },
  { isoLang: "ps", label: "Pashto" },
  { isoLang: "fa", label: "Persian" },
  { isoLang: "pl", label: "Polish" },
  { isoLang: "pt", label: "Portuguese" },
  { isoLang: "pa", label: "Punjabi" },
  { isoLang: "qu", label: "Quechua" },
  { isoLang: "ro", label: "Romanian" },
  { isoLang: "rm", label: "Romansh" },
  { isoLang: "rn", label: "Rundi" },
  { isoLang: "ru", label: "Russian" },
  { isoLang: "se", label: "Northern Sami" },
  { isoLang: "sm", label: "Samoan" },
  { isoLang: "sg", label: "Sango" },
  { isoLang: "sa", label: "Sanskrit" },
  { isoLang: "sc", label: "Sardinian" },
  { isoLang: "sr", label: "Serbian" },
  { isoLang: "sn", label: "Shona" },
  { isoLang: "ii", label: "Sichuan Yi" },
  { isoLang: "sd", label: "Sindhi" },
  { isoLang: "si", label: "Sinhala" },
  { isoLang: "sk", label: "Slovak" },
  { isoLang: "sl", label: "Slovenian" },
  { isoLang: "so", label: "Somali" },
  { isoLang: "st", label: "Southern Sotho" },
  { isoLang: "es", label: "Spanish" },
  { isoLang: "su", label: "Sundanese" },
  { isoLang: "sw", label: "Swahili" },
  { isoLang: "ss", label: "Swati" },
  { isoLang: "sv", label: "Swedish" },
  { isoLang: "tl", label: "Tagalog" },
  { isoLang: "ty", label: "Tahitian" },
  { isoLang: "tg", label: "Tajik" },
  { isoLang: "ta", label: "Tamil" },
  { isoLang: "tt", label: "Tatar" },
  { isoLang: "te", label: "Telugu" },
  { isoLang: "th", label: "Thai" },
  { isoLang: "bo", label: "Tibetan" },
  { isoLang: "ti", label: "Tigrinya" },
  { isoLang: "to", label: "Tonga" },
  { isoLang: "ts", label: "Tsonga" },
  { isoLang: "tn", label: "Tswana" },
  { isoLang: "tr", label: "Turkish" },
  { isoLang: "tk", label: "Turkmen" },
  { isoLang: "tw", label: "Twi" },
  { isoLang: "ug", label: "Uighur" },
  { isoLang: "uk", label: "Ukrainian" },
  { isoLang: "ur", label: "Urdu" },
  { isoLang: "uz", label: "Uzbek" },
  { isoLang: "ve", label: "Venda" },
  { isoLang: "vi", label: "Vietnamese" },
  { isoLang: "vo", label: "Volapük" },
  { isoLang: "wa", label: "Walloon" },
  { isoLang: "cy", label: "Welsh" },
  { isoLang: "wo", label: "Wolof" },
  { isoLang: "xh", label: "Xhosa" },
  { isoLang: "yi", label: "Yiddish" },
  { isoLang: "yo", label: "Yoruba" },
  { isoLang: "za", label: "Zhuang" },
  { isoLang: "zu", label: "Zulu" },
];

export const DEFAULT_SETTINGS: LangCardsSettings = {
  originalLang: LANGUAGE_OPTIONS.find((o) => o.isoLang === "fr")!,
  translatedLang: LANGUAGE_OPTIONS.find((o) => o.isoLang === "ru")!,
};
